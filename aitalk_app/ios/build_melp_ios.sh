#!/bin/bash

# MELP iOS构建脚本
# 编译MELP源码为iOS静态库并集成到项目中

set -e

echo "开始构建MELP iOS静态库..."

# 设置路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MELP_SRC_DIR="$PROJECT_ROOT/shared/melp/MELPe_fxp"
BUILD_DIR="$SCRIPT_DIR/build"
MELP_CODEC_DIR="$SCRIPT_DIR/Runner/MelpCodec"

# 检查MELP源码目录
if [ ! -d "$MELP_SRC_DIR" ]; then
    echo "错误: MELP源码目录不存在: $MELP_SRC_DIR"
    exit 1
fi

# 创建构建目录
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

echo "编译MELP源码..."

# 设置编译器标志
CFLAGS="-I$MELP_SRC_DIR -I$MELP_CODEC_DIR -O2 -fPIC"
ARCH_FLAGS="-arch arm64 -arch x86_64"

# 收集MELP源文件（排除包含main函数的文件）
MELP_SOURCES=""
for file in "$MELP_SRC_DIR"/*.c; do
    filename=$(basename "$file")
    case "$filename" in
        melp_enc.c|melp_dec.c|sc12enc.c|sc24enc.c|sc12dec.c|sc24dec.c|sc1200.c)
            echo "跳过包含main函数的文件: $filename"
            ;;
        *)
            MELP_SOURCES="$MELP_SOURCES $file"
            ;;
    esac
done

# 添加Win32/mathhalf.c
if [ -f "$MELP_SRC_DIR/Win32/mathhalf.c" ]; then
    MELP_SOURCES="$MELP_SOURCES $MELP_SRC_DIR/Win32/mathhalf.c"
fi

# 添加iOS包装器源文件
MELP_SOURCES="$MELP_SOURCES $MELP_CODEC_DIR/melp_ios.c"

echo "编译源文件..."
echo "源文件列表: $MELP_SOURCES"

# 编译为目标文件
clang $CFLAGS $ARCH_FLAGS -c $MELP_SOURCES

# 创建静态库
echo "创建静态库..."
ar rcs libmelp.a *.o

# 验证静态库
if [ -f "libmelp.a" ]; then
    echo "静态库创建成功: $BUILD_DIR/libmelp.a"
    file libmelp.a
    echo ""
    echo "库中的符号:"
    nm -g libmelp.a | grep -E "(melp_init|melp_encode|melp_decode)" | head -10
else
    echo "错误: 静态库创建失败"
    exit 1
fi

echo ""
echo "MELP iOS静态库构建完成!"
echo "静态库位置: $BUILD_DIR/libmelp.a"
echo ""
echo "下一步:"
echo "1. 在Xcode中将libmelp.a添加到项目"
echo "2. 添加头文件搜索路径: $MELP_SRC_DIR 和 $MELP_CODEC_DIR"
echo "3. 添加MelpWrapper.h和MelpWrapper.m到项目"
echo "4. 在Runner-Bridging-Header.h中导入MelpWrapper.h"
