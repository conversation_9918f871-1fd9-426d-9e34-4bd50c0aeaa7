# Xcode集成步骤 - 解决符号未找到问题

## 问题分析
错误 `Failed to lookup symbol 'melp_init': symbol not found` 表明MELP函数没有被导出到主程序符号表中。

## 解决方案：在Xcode中直接编译源文件

### 步骤1：打开Xcode项目
```bash
cd ios
open Runner.xcworkspace
```

### 步骤2：添加MELP源文件到项目

#### 2.1 添加MelpCodec组
1. 右键点击Runner项目 → "New Group"
2. 命名为 "MelpCodec"

#### 2.2 添加源文件
将以下文件拖拽到MelpCodec组中（选择"Copy items if needed"）：

**iOS包装器文件：**
- `Runner/MelpCodec/melp_ios.h`
- `Runner/MelpCodec/melp_ios.c`
- `Runner/MelpCodec/MelpWrapper.h`
- `Runner/MelpCodec/MelpWrapper.m`

**MELP核心源文件：**
从 `../shared/melp/MELPe_fxp/` 添加所有 `.c` 文件，**除了以下文件**（这些包含main函数）：
- ❌ `melp_enc.c`
- ❌ `melp_dec.c`
- ❌ `sc12enc.c`
- ❌ `sc24enc.c`
- ❌ `sc12dec.c`
- ❌ `sc24dec.c`
- ❌ `sc1200.c`

**Win32数学库：**
- `../shared/melp/MELPe_fxp/Win32/mathhalf.c`

### 步骤3：配置Build Settings

#### 3.1 Header Search Paths
在Runner target的Build Settings中，找到"Header Search Paths"，添加：
```
$(SRCROOT)/../shared/melp/MELPe_fxp
$(SRCROOT)/Runner/MelpCodec
```

#### 3.2 Other C Flags
在"Other C Flags"中添加：
```
-U__arm__
```

#### 3.3 确保Bridging Header正确
确认"Objective-C Bridging Header"设置为：
```
Runner/Runner-Bridging-Header.h
```

### 步骤4：验证Bridging Header内容
确认 `Runner/Runner-Bridging-Header.h` 包含：
```objc
#import "GeneratedPluginRegistrant.h"
#import "MelpCodec/MelpWrapper.h"
```

### 步骤5：编译测试
1. 选择iOS模拟器作为目标
2. 按 Cmd+B 编译项目
3. 检查是否有编译错误

### 步骤6：运行测试
```bash
flutter run -d ios
```

## 预期结果
- 编译无错误
- 应用启动时不再出现 "symbol not found" 错误
- MELP编解码功能正常工作

## 故障排除

### 如果仍然出现符号未找到错误：
1. 检查所有MELP源文件是否都添加到了项目中
2. 确认Build Settings中的路径配置正确
3. 清理项目：Product → Clean Build Folder
4. 重新编译

### 如果出现编译错误：
1. 检查头文件路径是否正确
2. 确认没有重复的符号定义
3. 检查是否排除了包含main函数的文件

### 如果FFI仍然无法加载符号：
考虑修改Dart代码，直接调用Objective-C方法而不是使用FFI：

```dart
// 在iOS平台使用MethodChannel而不是FFI
if (Platform.isIOS) {
  // 使用MethodChannel调用MelpWrapper
} else if (Platform.isAndroid) {
  // 使用FFI调用JNI
}
```

## 注意事项
1. 不要添加静态库libmelp.a到项目中，直接编译源文件
2. 确保所有源文件都在同一个target中
3. 如果使用CocoaPods，可能需要在Podfile中配置额外的编译标志
