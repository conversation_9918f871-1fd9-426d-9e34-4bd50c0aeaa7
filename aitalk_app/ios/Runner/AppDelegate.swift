import Flutter
import UIKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)

    // 注册MELP MethodChannel
    let controller = window?.rootViewController as! FlutterViewController
    MelpMethodChannel.register(with: registrar(forPlugin: "MelpMethodChannel")!)

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
