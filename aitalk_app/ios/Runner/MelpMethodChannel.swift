import Flutter
import UIKit

@objc public class MelpMethodChannel: NSObject, FlutterPlugin {
    private static let channelName = "com.aitalk.melp"
    private var melpWrapper: MelpWrapper?
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: channel<PERSON>ame, binaryMessenger: registrar.messenger())
        let instance = MelpMethodChannel()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "init":
            handleInit(call, result: result)
        case "setRate":
            handleSetRate(call, result: result)
        case "encode":
            handleEncode(call, result: result)
        case "decode":
            handleDecode(call, result: result)
        case "dispose":
            handleDispose(call, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func handleInit(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let rate = args["rate"] as? Int else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing rate parameter", details: nil))
            return
        }
        
        melpWrapper = MelpWrapper.shared()
        let success = melpWrapper?.initialize(withRate: Int32(rate)) ?? false
        
        if success {
            result(nil)
        } else {
            result(FlutterError(code: "INIT_FAILED", message: "Failed to initialize MELP codec", details: nil))
        }
    }
    
    private func handleSetRate(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let rate = args["rate"] as? Int,
              let wrapper = melpWrapper else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing rate parameter or codec not initialized", details: nil))
            return
        }
        
        let success = wrapper.setRate(Int32(rate))
        
        if success {
            result(nil)
        } else {
            result(FlutterError(code: "SET_RATE_FAILED", message: "Failed to set MELP rate", details: nil))
        }
    }
    
    private func handleEncode(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let pcmData = args["pcm"] as? FlutterStandardTypedData,
              let wrapper = melpWrapper else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing PCM data or codec not initialized", details: nil))
            return
        }
        
        let encodedData = wrapper.encode(pcmData.data)
        
        if let encoded = encodedData {
            result(FlutterStandardTypedData(bytes: encoded))
        } else {
            result(FlutterError(code: "ENCODE_FAILED", message: "Failed to encode PCM data", details: nil))
        }
    }
    
    private func handleDecode(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let encodedData = args["encoded"] as? FlutterStandardTypedData,
              let wrapper = melpWrapper else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing encoded data or codec not initialized", details: nil))
            return
        }
        
        let decodedData = wrapper.decode(encodedData.data)
        
        if let decoded = decodedData {
            result(FlutterStandardTypedData(bytes: decoded))
        } else {
            result(FlutterError(code: "DECODE_FAILED", message: "Failed to decode data", details: nil))
        }
    }
    
    private func handleDispose(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        melpWrapper = nil
        result(nil)
    }
}
