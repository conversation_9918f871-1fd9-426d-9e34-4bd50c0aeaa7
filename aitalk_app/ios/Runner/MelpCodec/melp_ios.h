#ifndef MELP_IOS_H
#define MELP_IOS_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// Rate constants (in bps)
#define MELP_RATE_2400 2400
#define MELP_RATE_1200 1200

// 导出属性定义
#if defined(__GNUC__) && __GNUC__ >= 4
    #define MELP_EXPORT __attribute__((visibility("default")))
#else
    #define MELP_EXPORT
#endif

// 初始化内部 MELP 编解码器（rate_bps 取 2400/1200），返回 0/ -1
MELP_EXPORT int melp_init(int rate_bps);

// 切换速率（2400 / 1200），会触发内部重新 init，返回 0 成功
MELP_EXPORT int melp_set_rate(int rate_bps);

// 编码 — 输入 pcm(short) *frameSamples 到比特流，返回输出字节数 (2400=>7, 1200=>11)
MELP_EXPORT int melp_encode_frame(const int16_t *pcm, uint8_t *bits);

// 解码 — 输入比特流到 pcm(short) *frameSamples，返回 0 成功
MELP_EXPORT int melp_decode_frame(const uint8_t *bits, int16_t *pcm);

#ifdef __cplusplus
}
#endif

#endif // MELP_IOS_H
