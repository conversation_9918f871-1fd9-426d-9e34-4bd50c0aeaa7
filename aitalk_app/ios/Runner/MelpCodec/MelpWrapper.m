#import "MelpWrapper.h"
#import "melp_ios.h"

@interface MelpWrapper ()
@property (nonatomic, assign) int currentRate;
@property (nonatomic, assign) BOOL initialized;
@end

@implementation MelpWrapper

+ (instancetype)sharedInstance {
    static MelpWrapper *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[MelpWrapper alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _currentRate = MELP_RATE_2400;
        _initialized = NO;
    }
    return self;
}

- (int)initWithRate:(int)rate {
    if (rate != MELP_RATE_1200 && rate != MELP_RATE_2400) {
        NSLog(@"[MelpWrapper] Invalid rate: %d. Must be 1200 or 2400.", rate);
        return -1;
    }
    
    int result = melp_init(rate);
    if (result == 0) {
        _currentRate = rate;
        _initialized = YES;
        NSLog(@"[MelpWrapper] Initialized with rate: %d", rate);
    } else {
        NSLog(@"[MelpWrapper] Failed to initialize with rate: %d", rate);
    }
    
    return result;
}

- (int)setRate:(int)rate {
    if (rate != MELP_RATE_1200 && rate != MELP_RATE_2400) {
        NSLog(@"[MelpWrapper] Invalid rate: %d. Must be 1200 or 2400.", rate);
        return -1;
    }
    
    int result = melp_set_rate(rate);
    if (result == 0) {
        _currentRate = rate;
        NSLog(@"[MelpWrapper] Rate changed to: %d", rate);
    } else {
        NSLog(@"[MelpWrapper] Failed to change rate to: %d", rate);
    }
    
    return result;
}

- (nullable NSData *)encodeFrame:(NSData *)pcmData {
    if (!_initialized) {
        NSLog(@"[MelpWrapper] Encoder not initialized. Call initWithRate: first.");
        return nil;
    }
    
    if (!pcmData) {
        NSLog(@"[MelpWrapper] PCM data is nil");
        return nil;
    }
    
    int expectedSamples = [self frameSize];
    int expectedBytes = expectedSamples * sizeof(int16_t);
    
    if (pcmData.length != expectedBytes) {
        NSLog(@"[MelpWrapper] Invalid PCM data length: %lu, expected: %d", 
              (unsigned long)pcmData.length, expectedBytes);
        return nil;
    }
    
    const int16_t *pcm = (const int16_t *)pcmData.bytes;
    int encodedSize = [self encodedFrameSize];
    uint8_t *bits = malloc(encodedSize);
    
    if (!bits) {
        NSLog(@"[MelpWrapper] Failed to allocate memory for encoded data");
        return nil;
    }
    
    int result = melp_encode_frame(pcm, bits);
    
    if (result <= 0) {
        NSLog(@"[MelpWrapper] Encoding failed with result: %d", result);
        free(bits);
        return nil;
    }
    
    NSData *encodedData = [NSData dataWithBytes:bits length:encodedSize];
    free(bits);
    
    return encodedData;
}

- (nullable NSData *)decodeFrame:(NSData *)bitsData {
    if (!_initialized) {
        NSLog(@"[MelpWrapper] Decoder not initialized. Call initWithRate: first.");
        return nil;
    }
    
    if (!bitsData) {
        NSLog(@"[MelpWrapper] Bits data is nil");
        return nil;
    }
    
    int expectedBits = [self encodedFrameSize];
    
    if (bitsData.length != expectedBits) {
        NSLog(@"[MelpWrapper] Invalid bits data length: %lu, expected: %d", 
              (unsigned long)bitsData.length, expectedBits);
        return nil;
    }
    
    const uint8_t *bits = (const uint8_t *)bitsData.bytes;
    int frameSize = [self frameSize];
    int16_t *pcm = malloc(frameSize * sizeof(int16_t));
    
    if (!pcm) {
        NSLog(@"[MelpWrapper] Failed to allocate memory for decoded data");
        return nil;
    }
    
    int result = melp_decode_frame(bits, pcm);
    
    if (result != 0) {
        NSLog(@"[MelpWrapper] Decoding failed with result: %d", result);
        free(pcm);
        return nil;
    }
    
    NSData *decodedData = [NSData dataWithBytes:pcm length:frameSize * sizeof(int16_t)];
    free(pcm);
    
    return decodedData;
}

- (int)frameSize {
    return (_currentRate == MELP_RATE_2400) ? 180 : 540;
}

- (int)encodedFrameSize {
    return (_currentRate == MELP_RATE_2400) ? 7 : 11;
}

@end
