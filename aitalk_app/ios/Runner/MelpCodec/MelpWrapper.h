#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * MELP音频编解码器Objective-C包装器
 * 
 * 支持MELP 1.2kbps和2.4kbps两种比特率的编解码
 * 提供线程安全的单例接口
 */
@interface MelpWrapper : NSObject

/**
 * 获取单例实例
 */
+ (instancetype)sharedInstance;

/**
 * 初始化MELP编解码器
 * @param rate 比特率，支持1200或2400
 * @return 0表示成功，-1表示失败
 */
- (int)initWithRate:(int)rate;

/**
 * 设置编解码器比特率
 * @param rate 比特率，支持1200或2400
 * @return 0表示成功，-1表示失败
 */
- (int)setRate:(int)rate;

/**
 * 编码单帧PCM数据
 * @param pcmData PCM数据，长度必须等于帧大小（2400bps: 180样本, 1200bps: 540样本）
 * @return 编码后的字节数据，失败返回nil
 */
- (nullable NSData *)encodeFrame:(NSData *)pcmData;

/**
 * 解码单帧比特流数据
 * @param bitsData 比特流数据（2400bps: 7字节, 1200bps: 11字节）
 * @return 解码后的PCM数据，失败返回nil
 */
- (nullable NSData *)decodeFrame:(NSData *)bitsData;

/**
 * 获取当前比特率对应的帧大小（样本数）
 * @return 帧大小（2400bps: 180, 1200bps: 540）
 */
- (int)frameSize;

/**
 * 获取当前比特率对应的编码字节数
 * @return 编码字节数（2400bps: 7, 1200bps: 11）
 */
- (int)encodedFrameSize;

@end

NS_ASSUME_NONNULL_END
