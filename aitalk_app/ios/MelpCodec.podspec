Pod::Spec.new do |s|
  s.name             = 'MelpCodec'
  s.version          = '1.0.0'
  s.summary          = 'MELP Audio Codec for iOS'
  s.description      = <<-DESC
                       MELP (Mixed Excitation Linear Prediction) audio codec implementation for iOS.
                       Supports 1.2kbps and 2.4kbps encoding/decoding.
                       DESC
  s.homepage         = 'https://github.com/your-repo/aitalk'
  s.license          = { :type => 'MIT', :file => 'LICENSE' }
  s.author           = { 'aiTalk Team' => '<EMAIL>' }
  s.source           = { :path => '.' }

  s.ios.deployment_target = '15.0'

  # 源文件
  s.source_files = [
    'Runner/MelpCodec/*.{h,m,c}',
    '../shared/melp/MELPe_fxp/*.c',
    '../shared/melp/MELPe_fxp/Win32/*.c'
  ]
  
  # 排除包含main函数的文件
  s.exclude_files = [
    '../shared/melp/MELPe_fxp/melp_enc.c',
    '../shared/melp/MELPe_fxp/melp_dec.c',
    '../shared/melp/MELPe_fxp/sc12enc.c',
    '../shared/melp/MELPe_fxp/sc24enc.c',
    '../shared/melp/MELPe_fxp/sc12dec.c',
    '../shared/melp/MELPe_fxp/sc24dec.c',
    '../shared/melp/MELPe_fxp/sc1200.c'
  ]

  # 头文件
  s.public_header_files = 'Runner/MelpCodec/MelpWrapper.h'

  # 编译器标志和头文件搜索路径
  s.xcconfig = {
    'HEADER_SEARCH_PATHS' => '$(SRCROOT)/../shared/melp/MELPe_fxp $(SRCROOT)/Runner/MelpCodec',
    'OTHER_CFLAGS' => '-U__arm__'
  }

  # 框架依赖
  s.frameworks = 'Foundation'

  # 静态库
  s.vendored_libraries = 'build/libmelp.a'

  # 要求ARC
  s.requires_arc = true
end
