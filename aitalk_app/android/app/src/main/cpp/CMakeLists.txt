cmake_minimum_required(VERSION 3.10.2)
project(melp_jni)

# 使用共享的MELP源码目录
set(MELP_SRC_DIR ${CMAKE_CURRENT_LIST_DIR}/../../../../../shared/melp/MELPe_fxp)

# 收集 MELP 源文件，排除含有 main() 的演示程序
file(GLOB MELP_C_FILES "${MELP_SRC_DIR}/*.c")
list(REMOVE_ITEM MELP_C_FILES
        ${MELP_SRC_DIR}/melp_enc.c
        ${MELP_SRC_DIR}/melp_dec.c
        ${MELP_SRC_DIR}/sc12enc.c
        ${MELP_SRC_DIR}/sc24enc.c
        ${MELP_SRC_DIR}/sc12dec.c
        ${MELP_SRC_DIR}/sc24dec.c
        ${MELP_SRC_DIR}/sc1200.c)

# 额外源文件
list(APPEND MELP_C_FILES ${MELP_SRC_DIR}/Win32/mathhalf.c)

add_library(melp STATIC ${MELP_C_FILES})

target_include_directories(melp PUBLIC ${MELP_SRC_DIR})

target_link_libraries(melp)

find_library(
        log-lib
        log)

add_library(
        melp_jni
        SHARED
        melp_jni.c
        melp_wrapper.c)

target_include_directories(melp_jni PRIVATE ${MELP_SRC_DIR} ${CMAKE_CURRENT_LIST_DIR})

target_link_libraries(melp_jni melp ${log-lib}) 

target_compile_options(melp PRIVATE -U__arm__) 