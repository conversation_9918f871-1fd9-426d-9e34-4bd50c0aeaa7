# MELP Codec iOS集成指南

## 概述
本指南详细说明如何将MELP Codec集成到iOS项目中，实现与Android平台相同的编解码功能。

## 已完成的工作

### 1. 目录结构重构 ✅
- 创建了 `shared/melp/MELPe_fxp/` 共享源码目录
- 将MELP源码从Android专用目录移动到共享位置
- 更新了Android CMakeLists.txt以引用新路径

### 2. iOS原生代码实现 ✅
- 创建了 `ios/Runner/MelpCodec/` 目录
- 实现了C接口层：`melp_ios.h` 和 `melp_ios.c`
- 实现了Objective-C包装器：`MelpWrapper.h` 和 `MelpWrapper.m`
- 提供了与Android wrapper相同的API接口

### 3. FFI接口统一 ✅
- 修改了 `lib/core/audio/melp_ffi.dart` 支持iOS平台
- iOS使用 `DynamicLibrary.process()` 加载主程序符号
- 保持了与Android相同的Dart接口

### 4. 静态库构建 ✅
- 创建了 `ios/build_melp_ios.sh` 构建脚本
- 成功编译了多架构静态库 `libmelp.a` (arm64 + x86_64)
- 验证了关键符号导出：`melp_init`, `melp_encode_frame`, `melp_decode_frame`

## 手动集成步骤

### 步骤1：在Xcode中添加源文件
1. 打开 `ios/Runner.xcodeproj`
2. 右键点击Runner组，选择"Add Files to Runner"
3. 添加以下文件：
   - `ios/Runner/MelpCodec/MelpWrapper.h`
   - `ios/Runner/MelpCodec/MelpWrapper.m`
   - `ios/Runner/MelpCodec/melp_ios.c`
   - `ios/build/libmelp.a`

### 步骤2：配置头文件搜索路径
1. 选择Runner项目 → Build Settings
2. 搜索"Header Search Paths"
3. 添加以下路径：
   ```
   $(SRCROOT)/../shared/melp/MELPe_fxp
   $(SRCROOT)/Runner/MelpCodec
   ```

### 步骤3：配置编译标志
1. 在Build Settings中搜索"Other C Flags"
2. 添加：`-U__arm__`

### 步骤4：验证Bridging Header
确认 `ios/Runner/Runner-Bridging-Header.h` 包含：
```objc
#import "GeneratedPluginRegistrant.h"
#import "MelpCodec/MelpWrapper.h"
```

## 测试验证

### 编译测试
```bash
cd ios
xcodebuild -workspace Runner.xcworkspace -scheme Runner -destination 'platform=iOS Simulator,name=iPhone 15' build
```

### 功能测试
在Dart代码中测试MELP编解码：
```dart
final codec = MelpAudioCodec(rate: 2400);
await codec.init();

// 测试编码
final pcmData = Int16List.fromList([...]);  // 180个样本
final encoded = codec.encode(pcmData);

// 测试解码
final decoded = codec.decode(encoded);
```

## 关键文件说明

### `melp_ios.h/c`
- 提供与Android wrapper相同的C接口
- 函数：`melp_init`, `melp_set_rate`, `melp_encode_frame`, `melp_decode_frame`

### `MelpWrapper.h/m`
- Objective-C包装器，提供面向对象接口
- 线程安全的单例模式
- 内存管理和错误处理

### `melp_ffi.dart`
- 统一的FFI接口，支持Android和iOS
- iOS使用 `DynamicLibrary.process()` 加载符号

## 故障排除

### 编译错误
1. **找不到头文件**：检查Header Search Paths配置
2. **符号未定义**：确认libmelp.a已添加到项目
3. **架构不匹配**：重新运行构建脚本生成静态库

### 运行时错误
1. **FFI加载失败**：确认符号已正确导出到主程序
2. **编解码失败**：检查输入数据格式和长度

## 下一步
1. 在真机上测试编译和运行
2. 验证编解码结果与Android平台一致性
3. 性能测试和优化
4. 集成到CI/CD流程
