/// MELP iOS集成测试脚本
/// 
/// 用于验证MELP Codec在iOS平台的基本功能
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'lib/core/audio/melp_codec.dart';

void main() {
  group('MELP iOS Integration Tests', () {
    late MelpAudioCodec codec2400;
    late MelpAudioCodec codec1200;

    setUpAll(() async {
      // 只在iOS平台运行测试
      if (!Platform.isIOS) {
        print('跳过测试：当前平台不是iOS');
        return;
      }

      print('初始化MELP编解码器...');
      
      // 初始化2400bps编解码器
      codec2400 = MelpAudioCodec(rate: 2400);
      await codec2400.init();
      
      // 初始化1200bps编解码器
      codec1200 = MelpAudioCodec(rate: 1200);
      await codec1200.init();
      
      print('MELP编解码器初始化完成');
    });

    tearDownAll(() async {
      if (Platform.isIOS) {
        await codec2400.dispose();
        await codec1200.dispose();
        print('MELP编解码器已释放');
      }
    });

    test('MELP 2400bps 编解码测试', () async {
      if (!Platform.isIOS) return;

      print('测试MELP 2400bps编解码...');
      
      // 创建测试PCM数据（180个样本）
      final pcmData = Int16List.fromList(
        List.generate(180, (i) => (1000 * (i % 100 - 50)).toInt())
      );
      
      print('原始PCM数据长度: ${pcmData.length}');
      
      // 编码
      final encoded = codec2400.encode(pcmData);
      print('编码后数据长度: ${encoded.length} 字节');
      expect(encoded.length, equals(7)); // 2400bps应该产生7字节
      
      // 解码
      final decoded = codec2400.decode(encoded);
      print('解码后PCM数据长度: ${decoded.length}');
      expect(decoded.length, equals(180)); // 应该恢复为180个样本
      
      print('MELP 2400bps编解码测试通过');
    });

    test('MELP 1200bps 编解码测试', () async {
      if (!Platform.isIOS) return;

      print('测试MELP 1200bps编解码...');
      
      // 创建测试PCM数据（540个样本）
      final pcmData = Int16List.fromList(
        List.generate(540, (i) => (800 * (i % 200 - 100)).toInt())
      );
      
      print('原始PCM数据长度: ${pcmData.length}');
      
      // 编码
      final encoded = codec1200.encode(pcmData);
      print('编码后数据长度: ${encoded.length} 字节');
      expect(encoded.length, equals(11)); // 1200bps应该产生11字节
      
      // 解码
      final decoded = codec1200.decode(encoded);
      print('解码后PCM数据长度: ${decoded.length}');
      expect(decoded.length, equals(540)); // 应该恢复为540个样本
      
      print('MELP 1200bps编解码测试通过');
    });

    test('错误处理测试', () async {
      if (!Platform.isIOS) return;

      print('测试错误处理...');
      
      // 测试错误的PCM数据长度
      final wrongSizePcm = Int16List.fromList([1, 2, 3]); // 错误长度
      
      expect(() => codec2400.encode(wrongSizePcm), throwsException);
      print('错误PCM长度处理正确');
      
      // 测试错误的编码数据长度
      final wrongSizeBits = Uint8List.fromList([1, 2, 3]); // 错误长度
      
      expect(() => codec2400.decode(wrongSizeBits), throwsException);
      print('错误编码数据长度处理正确');
      
      print('错误处理测试通过');
    });

    test('比特率切换测试', () async {
      if (!Platform.isIOS) return;

      print('测试比特率切换...');
      
      // 创建2400bps测试数据
      final pcm2400 = Int16List.fromList(
        List.generate(180, (i) => (500 * (i % 50 - 25)).toInt())
      );
      
      // 编码为2400bps
      final encoded2400 = codec2400.encode(pcm2400);
      expect(encoded2400.length, equals(7));
      
      // 创建1200bps测试数据
      final pcm1200 = Int16List.fromList(
        List.generate(540, (i) => (600 * (i % 60 - 30)).toInt())
      );
      
      // 编码为1200bps
      final encoded1200 = codec1200.encode(pcm1200);
      expect(encoded1200.length, equals(11));
      
      print('比特率切换测试通过');
    });

    test('性能测试', () async {
      if (!Platform.isIOS) return;

      print('开始性能测试...');
      
      final pcmData = Int16List.fromList(
        List.generate(180, (i) => (1000 * (i % 100 - 50)).toInt())
      );
      
      final stopwatch = Stopwatch()..start();
      
      // 执行100次编解码
      for (int i = 0; i < 100; i++) {
        final encoded = codec2400.encode(pcmData);
        final decoded = codec2400.decode(encoded);
        expect(decoded.length, equals(180));
      }
      
      stopwatch.stop();
      final avgTime = stopwatch.elapsedMicroseconds / 100;
      
      print('100次编解码平均耗时: ${avgTime.toStringAsFixed(2)} 微秒');
      print('性能测试完成');
    });
  });
}

/// 运行测试的辅助函数
void runMelpTests() async {
  print('=== MELP iOS集成测试开始 ===');
  
  if (!Platform.isIOS) {
    print('当前平台: ${Platform.operatingSystem}');
    print('此测试仅在iOS平台运行');
    return;
  }
  
  try {
    // 这里应该调用实际的测试框架
    print('请使用 flutter test 命令运行此测试文件');
    print('或者在iOS设备/模拟器上运行应用进行集成测试');
  } catch (e) {
    print('测试执行出错: $e');
  }
  
  print('=== MELP iOS集成测试结束 ===');
}
