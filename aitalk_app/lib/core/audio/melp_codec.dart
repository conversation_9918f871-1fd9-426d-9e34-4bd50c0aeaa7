/// MELP音频编解码器实现
///
/// 支持MELP 1.2kbps和2.4kbps两种比特率的编解码
/// 实现AudioCodec接口，提供统一的编解码API
library melp_codec;

import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'audio_codec.dart';
import 'melp_ffi.dart';

/// MELP编解码器实现类
///
/// 支持的比特率：
/// - 2400bps: 帧大小180样本，编码后7字节
/// - 1200bps: 帧大小540样本，编码后11字节
///
/// 使用示例：
/// ```dart
/// final codec = MelpAudioCodec(rate: 2400);
/// await codec.init();
///
/// // 编码
/// final pcmData = Int16List.fromList([...]);  // 180或540个样本
/// final encoded = codec.encode(pcmData);
///
/// // 解码
/// final decoded = codec.decode(encoded);
///
/// await codec.dispose();
/// ```
class MelpAudioCodec implements AudioCodec {
  final int _rate;
  late final MelpCodec _melpCodec;
  bool _initialized = false;

  /// 创建MELP编解码器
  ///
  /// [rate] 比特率，支持1200或2400
  MelpAudioCodec({int rate = 2400}) : _rate = rate {
    if (rate != 1200 && rate != 2400) {
      throw ArgumentError('MELP rate must be 1200 or 2400, got $rate');
    }
    _melpCodec = MelpCodec.instance;
  }

  /// 初始化编解码器
  Future<void> init() async {
    if (_initialized) return;

    try {
      await _melpCodec.init(rate: _rate);
      _initialized = true;
      debugPrint('MELP codec initialized with rate: $_rate');
    } catch (e) {
      debugPrint('Failed to initialize MELP codec: $e');
      rethrow;
    }
  }

  /// 编码单帧PCM数据
  ///
  /// [pcm] 输入的PCM数据，长度必须等于帧大小
  /// - 2400bps: 180个样本
  /// - 1200bps: 540个样本
  ///
  /// 返回编码后的字节数据
  @override
  Uint8List encode(Int16List pcm) {
    if (!_initialized) {
      throw StateError('MELP codec not initialized. Call init() first.');
    }

    try {
      // 注意：这里需要等待异步结果，但接口是同步的
      // 在实际使用中，建议修改接口为异步
      throw UnimplementedError(
        'Synchronous encode not supported with MethodChannel. Use encodeAsync instead.',
      );
    } catch (e) {
      debugPrint('MELP encode error: $e');
      rethrow;
    }
  }

  /// 异步编码方法（推荐使用）
  Future<Uint8List> encodeAsync(Int16List pcm) async {
    if (!_initialized) {
      throw StateError('MELP codec not initialized. Call init() first.');
    }

    try {
      final pcmList = pcm.toList();
      final encodedList = await _melpCodec.encodeFrame(pcmList);
      return Uint8List.fromList(encodedList);
    } catch (e) {
      debugPrint('MELP encode error: $e');
      rethrow;
    }
  }

  /// 解码单帧编码数据
  ///
  /// [data] 编码后的字节数据
  /// - 2400bps: 7字节
  /// - 1200bps: 11字节
  ///
  /// 返回解码后的PCM数据
  @override
  Int16List decode(Uint8List data) {
    if (!_initialized) {
      throw StateError('MELP codec not initialized. Call init() first.');
    }

    try {
      final dataList = data.toList();
      // 注意：这里需要等待异步结果，但接口是同步的
      // 在实际使用中，建议修改接口为异步
      throw UnimplementedError(
        'Synchronous decode not supported with MethodChannel. Use decodeAsync instead.',
      );
    } catch (e) {
      debugPrint('MELP decode error: $e');
      rethrow;
    }
  }

  /// 异步解码方法（推荐使用）
  Future<Int16List> decodeAsync(Uint8List data) async {
    if (!_initialized) {
      throw StateError('MELP codec not initialized. Call init() first.');
    }

    try {
      final dataList = data.toList();
      final decodedList = await _melpCodec.decodeFrame(dataList);
      return Int16List.fromList(decodedList);
    } catch (e) {
      debugPrint('MELP decode error: $e');
      rethrow;
    }
  }

  /// 切换比特率
  ///
  /// [newRate] 新的比特率，支持1200或2400
  void setRate(int newRate) {
    if (!_initialized) {
      throw StateError('MELP codec not initialized. Call init() first.');
    }

    if (newRate != 1200 && newRate != 2400) {
      throw ArgumentError('MELP rate must be 1200 or 2400, got $newRate');
    }

    try {
      _melpCodec.setRate(newRate);
      debugPrint('MELP codec rate changed to: $newRate');
    } catch (e) {
      debugPrint('Failed to change MELP rate: $e');
      rethrow;
    }
  }

  /// 获取当前帧大小（样本数）
  int get frameSize {
    if (!_initialized) {
      throw StateError('MELP codec not initialized. Call init() first.');
    }
    return _melpCodec.frameSize;
  }

  /// 获取当前编码后字节数
  int get encodedBytesPerFrame {
    if (!_initialized) {
      throw StateError('MELP codec not initialized. Call init() first.');
    }
    return _melpCodec.encodedBytesPerFrame;
  }

  /// 获取当前比特率
  int get rate => _rate;

  /// 检查是否已初始化
  bool get isInitialized => _initialized;

  /// 释放资源
  @override
  Future<void> dispose() async {
    if (_initialized) {
      _initialized = false;
      debugPrint('MELP codec disposed');
    }
  }

  /// 编码+解码测试（用于验证编解码器功能）
  ///
  /// [pcm] 输入的PCM数据
  /// 返回经过编码再解码后的PCM数据
  Int16List processTest(Int16List pcm) {
    final encoded = encode(pcm);
    return decode(encoded);
  }
}

/// MELP编解码器工厂类
class MelpCodecFactory {
  /// 创建2.4kbps MELP编解码器
  static MelpAudioCodec create2400() => MelpAudioCodec(rate: 2400);

  /// 创建1.2kbps MELP编解码器
  static MelpAudioCodec create1200() => MelpAudioCodec(rate: 1200);

  /// 根据比特率创建编解码器
  static MelpAudioCodec createWithRate(int rate) => MelpAudioCodec(rate: rate);
}
