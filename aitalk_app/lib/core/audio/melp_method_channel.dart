import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';

/// MELP编解码器的MethodChannel实现（用于iOS平台）
class MelpMethodChannel {
  static const MethodChannel _channel = MethodChannel('com.aitalk.melp');
  
  bool _isInitialized = false;
  int _currentRate = 2400;
  
  /// 初始化MELP编解码器
  /// [rate] 比特率，支持1200或2400
  Future<void> init(int rate) async {
    if (!Platform.isIOS) {
      throw UnsupportedError('MethodChannel implementation is only for iOS');
    }
    
    try {
      await _channel.invokeMethod('init', {'rate': rate});
      _isInitialized = true;
      _currentRate = rate;
    } catch (e) {
      throw Exception('Failed to initialize MELP codec: $e');
    }
  }
  
  /// 设置比特率
  /// [rate] 比特率，支持1200或2400
  Future<void> setRate(int rate) async {
    if (!_isInitialized) {
      throw StateError('MELP codec not initialized');
    }
    
    if (rate != 1200 && rate != 2400) {
      throw ArgumentError('Unsupported rate: $rate. Only 1200 and 2400 are supported.');
    }
    
    try {
      await _channel.invokeMethod('setRate', {'rate': rate});
      _currentRate = rate;
    } catch (e) {
      throw Exception('Failed to set MELP rate: $e');
    }
  }
  
  /// 编码PCM音频数据
  /// [pcmData] 输入的PCM数据（16位有符号整数）
  /// 返回编码后的字节数据
  Future<Uint8List> encode(Int16List pcmData) async {
    if (!_isInitialized) {
      throw StateError('MELP codec not initialized');
    }
    
    // 验证输入数据长度
    final expectedLength = _currentRate == 2400 ? 180 : 540;
    if (pcmData.length != expectedLength) {
      throw ArgumentError(
        'Invalid PCM data length: ${pcmData.length}. '
        'Expected $expectedLength for ${_currentRate}bps rate.'
      );
    }
    
    try {
      // 将Int16List转换为字节数据
      final byteData = ByteData(pcmData.length * 2);
      for (int i = 0; i < pcmData.length; i++) {
        byteData.setInt16(i * 2, pcmData[i], Endian.little);
      }
      
      final result = await _channel.invokeMethod('encode', {
        'pcm': Uint8List.view(byteData.buffer)
      });
      
      if (result is Uint8List) {
        return result;
      } else {
        throw Exception('Unexpected result type from encode method');
      }
    } catch (e) {
      throw Exception('Failed to encode PCM data: $e');
    }
  }
  
  /// 解码音频数据
  /// [encodedData] 编码后的字节数据
  /// 返回解码后的PCM数据（16位有符号整数）
  Future<Int16List> decode(Uint8List encodedData) async {
    if (!_isInitialized) {
      throw StateError('MELP codec not initialized');
    }
    
    // 验证输入数据长度
    final expectedLength = _currentRate == 2400 ? 7 : 11;
    if (encodedData.length != expectedLength) {
      throw ArgumentError(
        'Invalid encoded data length: ${encodedData.length}. '
        'Expected $expectedLength for ${_currentRate}bps rate.'
      );
    }
    
    try {
      final result = await _channel.invokeMethod('decode', {
        'encoded': encodedData
      });
      
      if (result is Uint8List) {
        // 将字节数据转换为Int16List
        final byteData = ByteData.view(result.buffer);
        final pcmData = Int16List(result.length ~/ 2);
        
        for (int i = 0; i < pcmData.length; i++) {
          pcmData[i] = byteData.getInt16(i * 2, Endian.little);
        }
        
        return pcmData;
      } else {
        throw Exception('Unexpected result type from decode method');
      }
    } catch (e) {
      throw Exception('Failed to decode data: $e');
    }
  }
  
  /// 释放资源
  Future<void> dispose() async {
    if (_isInitialized) {
      try {
        await _channel.invokeMethod('dispose');
      } catch (e) {
        // 忽略释放时的错误
      } finally {
        _isInitialized = false;
      }
    }
  }
  
  /// 获取当前比特率
  int get currentRate => _currentRate;
  
  /// 检查是否已初始化
  bool get isInitialized => _isInitialized;
  
  /// 获取指定比特率的帧大小（样本数）
  static int getFrameSize(int rate) {
    switch (rate) {
      case 1200:
        return 540;
      case 2400:
        return 180;
      default:
        throw ArgumentError('Unsupported rate: $rate');
    }
  }
  
  /// 获取指定比特率的编码数据大小（字节数）
  static int getEncodedSize(int rate) {
    switch (rate) {
      case 1200:
        return 11;
      case 2400:
        return 7;
      default:
        throw ArgumentError('Unsupported rate: $rate');
    }
  }
}
