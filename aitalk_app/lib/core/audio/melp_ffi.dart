// MELP FFI binding
// 提供 melpInit(rate) / melpProcess(input) 简易接口
// Author: AI

import 'dart:ffi' as ffi;
import 'dart:io' show Platform;
import 'dart:typed_data';
import 'package:ffi/ffi.dart' as pkg_ffi;
import 'package:flutter/foundation.dart' show debugPrint;
import 'melp_method_channel.dart';

// 动态库加载
ffi.DynamicLibrary _openLib() {
  if (Platform.isAndroid) {
    return ffi.DynamicLibrary.open('libmelp_jni.so');
  } else if (Platform.isIOS) {
    // iOS使用主程序符号，MELP函数已静态链接到主程序
    return ffi.DynamicLibrary.process();
  }
  throw UnsupportedError('不支持的平台: ${Platform.operatingSystem}');
}

final _lib = _openLib();

// C 函数签名
typedef _melp_init_c = ffi.Int32 Function(ffi.Int32 rate);
typedef _melp_init_dart = int Function(int rate);

typedef _melp_set_rate_c = ffi.Int32 Function(ffi.Int32 rate);
typedef _melp_set_rate_dart = int Function(int rate);

typedef _melp_encode_c =
    ffi.Int32 Function(
      ffi.Pointer<ffi.Int16> pcmIn,
      ffi.Pointer<ffi.Uint8> bitsOut,
    );
typedef _melp_encode_dart =
    int Function(ffi.Pointer<ffi.Int16> pcmIn, ffi.Pointer<ffi.Uint8> bitsOut);

typedef _melp_decode_c =
    ffi.Int32 Function(
      ffi.Pointer<ffi.Uint8> bitsIn,
      ffi.Pointer<ffi.Int16> pcmOut,
    );
typedef _melp_decode_dart =
    int Function(ffi.Pointer<ffi.Uint8> bitsIn, ffi.Pointer<ffi.Int16> pcmOut);

// 可选：一次性分析+合成
typedef _melp_process_c =
    ffi.Int32 Function(
      ffi.Pointer<ffi.Int16> input,
      ffi.Pointer<ffi.Int16> output,
    );
typedef _melp_process_dart =
    int Function(ffi.Pointer<ffi.Int16> input, ffi.Pointer<ffi.Int16> output);

final _melpInit = _lib.lookupFunction<_melp_init_c, _melp_init_dart>(
  'melp_init',
);

final _melpSetRate = _lib.lookupFunction<_melp_set_rate_c, _melp_set_rate_dart>(
  'melp_set_rate',
);

final _melpEncode = _lib.lookupFunction<_melp_encode_c, _melp_encode_dart>(
  'melp_encode_frame',
);

final _melpDecode = _lib.lookupFunction<_melp_decode_c, _melp_decode_dart>(
  'melp_decode_frame',
);

// 可选：一次性分析+合成
final _melpProcess = _lib.lookupFunction<_melp_process_c, _melp_process_dart>(
  'melp_process',
);

class MelpCodec {
  MelpCodec._();
  static final MelpCodec instance = MelpCodec._();

  bool _inited = false;
  int _frameSize = 180; // 8kHz 2400bps 默认
  MelpMethodChannel? _methodChannel; // iOS平台使用MethodChannel

  Future<void> init({int rate = 2400}) async {
    if (_inited) return;

    if (Platform.isIOS) {
      // iOS平台使用MethodChannel作为备用方案
      try {
        _methodChannel = MelpMethodChannel();
        await _methodChannel!.init(rate);
        _inited = true;
        _frameSize = rate == 2400 ? 180 : 540;
        debugPrint('MELP initialized using MethodChannel on iOS');
        return;
      } catch (e) {
        debugPrint('MethodChannel initialization failed, trying FFI: $e');
        // 如果MethodChannel失败，继续尝试FFI
      }
    }

    // Android平台或iOS FFI方案
    try {
      final ret = _melpInit(rate);
      if (ret != 0) {
        throw Exception('melp_init failed: $ret');
      }
      _inited = true;
      _frameSize = rate == 2400 ? 180 : 540;
      debugPrint('MELP initialized using FFI');
    } catch (e) {
      if (Platform.isIOS) {
        throw Exception(
          'Both MethodChannel and FFI initialization failed on iOS: $e',
        );
      } else {
        throw Exception('FFI initialization failed: $e');
      }
    }
  }

  /// 切换速率（2400 / 1200），需在 init 之后调用
  Future<void> setRate(int rate) async {
    if (_methodChannel != null) {
      await _methodChannel!.setRate(rate);
      _frameSize = rate == 2400 ? 180 : 540;
      return;
    }

    final r = _melpSetRate(rate);
    if (r != 0) throw Exception('melp_set_rate failed: $r');
    _frameSize = rate == 2400 ? 180 : 540;
  }

  /// 编码一帧 → 返回 bitstream (长度 7 或 11 字节)
  Future<List<int>> encodeFrame(List<int> pcm) async {
    if (!_inited) {
      throw StateError('MelpCodec not initialized');
    }
    if (pcm.length != _frameSize) {
      throw ArgumentError('pcm length must be $_frameSize');
    }

    if (_methodChannel != null) {
      final result = await _methodChannel!.encode(Int16List.fromList(pcm));
      return result.toList();
    }
    final pcmPtr = pkg_ffi.malloc
        .allocate<ffi.Int16>(_frameSize * 2)
        .cast<ffi.Int16>();
    final bitsLen = _frameSize == 180 ? 7 : 11;
    final bitsPtr = pkg_ffi.malloc
        .allocate<ffi.Uint8>(bitsLen)
        .cast<ffi.Uint8>();

    for (var i = 0; i < _frameSize; ++i) {
      pcmPtr.elementAt(i).value = pcm[i];
    }
    final ret = _melpEncode(pcmPtr, bitsPtr);
    if (ret <= 0) {
      pkg_ffi.malloc.free(pcmPtr);
      pkg_ffi.malloc.free(bitsPtr);
      throw Exception('melp_encode_frame failed: $ret');
    }
    final bits = List<int>.generate(bitsLen, (i) => bitsPtr.elementAt(i).value);
    debugPrint(
      'Bits: ' + bits.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' '),
    );
    pkg_ffi.malloc.free(pcmPtr);
    pkg_ffi.malloc.free(bitsPtr);
    return bits;
  }

  /// 解码一帧 bitstream → 返回 PCM
  List<int> decodeFrame(List<int> bits) {
    if (!_inited) {
      throw StateError('MelpCodec not initialized');
    }
    final expectedBits = _frameSize == 180 ? 7 : 11;
    if (bits.length != expectedBits) {
      throw ArgumentError('bits length must be $expectedBits');
    }
    final bitsPtr = pkg_ffi.malloc
        .allocate<ffi.Uint8>(expectedBits)
        .cast<ffi.Uint8>();
    final pcmPtr = pkg_ffi.malloc
        .allocate<ffi.Int16>(_frameSize * 2)
        .cast<ffi.Int16>();
    for (var i = 0; i < expectedBits; ++i) {
      bitsPtr.elementAt(i).value = bits[i];
    }
    final ret = _melpDecode(bitsPtr, pcmPtr);
    if (ret != 0) {
      pkg_ffi.malloc.free(bitsPtr);
      pkg_ffi.malloc.free(pcmPtr);
      throw Exception('melp_decode_frame failed: $ret');
    }
    final pcm = List<int>.generate(
      _frameSize,
      (i) => pcmPtr.elementAt(i).value,
    );
    pkg_ffi.malloc.free(bitsPtr);
    pkg_ffi.malloc.free(pcmPtr);
    return pcm;
  }

  /// 输入 PCM int16 List 长度 frameSize，返回解码 PCM 同长 List
  List<int> process(List<int> pcm) {
    if (!_inited) {
      throw StateError('MelpCodec not initialized');
    }
    if (pcm.length != _frameSize) {
      throw ArgumentError('pcm length must be $_frameSize');
    }
    final inputPtr = pkg_ffi.malloc
        .allocate<ffi.Int16>(_frameSize * 2)
        .cast<ffi.Int16>();
    final outputPtr = pkg_ffi.malloc
        .allocate<ffi.Int16>(_frameSize * 2)
        .cast<ffi.Int16>();
    for (var i = 0; i < _frameSize; ++i) {
      inputPtr.elementAt(i).value = pcm[i];
    }
    final ret = _melpProcess(inputPtr, outputPtr);
    if (ret != 0) {
      pkg_ffi.malloc.free(inputPtr);
      pkg_ffi.malloc.free(outputPtr);
      throw Exception('melp_process failed: $ret');
    }
    final out = List<int>.generate(
      _frameSize,
      (i) => outputPtr.elementAt(i).value,
    );
    pkg_ffi.malloc.free(inputPtr);
    pkg_ffi.malloc.free(outputPtr);
    return out;
  }

  /// 获取当前帧大小
  int get frameSize => _frameSize;

  /// 获取当前比特率对应的编码字节数
  int get encodedBytesPerFrame => _frameSize == 180 ? 7 : 11;
}
