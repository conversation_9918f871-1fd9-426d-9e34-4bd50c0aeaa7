# MELP iOS符号未找到问题解决方案

## 问题分析
错误 `Failed to lookup symbol 'melp_init': symbol not found` 表明MELP函数没有被正确导出到iOS主程序符号表中。

## 解决方案：双重备用机制

我们实现了两种方案：
1. **FFI方案**：直接调用C函数（需要正确的Xcode配置）
2. **MethodChannel方案**：通过Swift/Objective-C桥接（更可靠）

## 已完成的工作

### 1. 创建了MethodChannel实现
- `ios/Runner/MelpMethodChannel.swift` - Swift端MethodChannel处理
- `lib/core/audio/melp_method_channel.dart` - Dart端MethodChannel接口

### 2. 修改了现有代码
- `lib/core/audio/melp_ffi.dart` - 添加了iOS平台的MethodChannel支持
- `lib/core/audio/melp_codec.dart` - 添加了异步编解码方法
- `ios/Runner/AppDelegate.swift` - 注册了MethodChannel
- `ios/Runner/MelpCodec/melp_ios.h` - 添加了符号导出属性

### 3. 智能回退机制
```dart
// 在MelpCodec.init()中：
if (Platform.isIOS) {
  try {
    // 首先尝试MethodChannel
    _methodChannel = MelpMethodChannel();
    await _methodChannel!.init(rate);
    // 成功则使用MethodChannel
  } catch (e) {
    // 失败则回退到FFI
    final ret = _melpInit(rate);
    // ...
  }
}
```

## 下一步操作

### 方案A：使用MethodChannel（推荐）

#### 1. 在Xcode中添加文件
打开 `ios/Runner.xcworkspace`，添加以下文件到项目：

**必须添加的文件：**
- `Runner/MelpMethodChannel.swift`
- `Runner/MelpCodec/MelpWrapper.h`
- `Runner/MelpCodec/MelpWrapper.m`
- `Runner/MelpCodec/melp_ios.c`

**MELP源文件（从 `../shared/melp/MELPe_fxp/` 添加所有.c文件，除了）：**
- ❌ `melp_enc.c`, `melp_dec.c`, `sc12enc.c`, `sc24enc.c`, `sc12dec.c`, `sc24dec.c`, `sc1200.c`
- ✅ 所有其他.c文件 + `Win32/mathhalf.c`

#### 2. 配置Build Settings
在Runner target的Build Settings中：
- **Header Search Paths**: 添加 `$(SRCROOT)/../shared/melp/MELPe_fxp` 和 `$(SRCROOT)/Runner/MelpCodec`
- **Other C Flags**: 添加 `-U__arm__`

#### 3. 测试编译
```bash
flutter build ios --debug
```

### 方案B：修复FFI方案

如果想继续使用FFI，需要确保：
1. 所有MELP源文件都编译到主程序中
2. 符号正确导出
3. 静态库正确链接

## 使用新的异步接口

由于MethodChannel是异步的，建议使用新的异步方法：

```dart
// 旧的同步方法（可能失败）
final encoded = codec.encode(pcmData);
final decoded = codec.decode(encodedData);

// 新的异步方法（推荐）
final encoded = await codec.encodeAsync(pcmData);
final decoded = await codec.decodeAsync(encodedData);
```

## 预期结果

### 成功标志：
1. **应用启动无错误** - 不再出现 "symbol not found"
2. **MELP初始化成功** - 日志显示 "MELP initialized using MethodChannel on iOS"
3. **编解码正常工作** - 音频处理功能正常

### 如果仍有问题：
1. 检查Xcode控制台的详细错误信息
2. 确认所有源文件都添加到了项目中
3. 验证Build Settings配置正确
4. 清理项目：Product → Clean Build Folder

## 技术优势

### MethodChannel方案优势：
- ✅ 更可靠的符号解析
- ✅ 更好的错误处理
- ✅ 符合iOS开发最佳实践
- ✅ 便于调试和维护

### FFI方案优势：
- ✅ 性能更高（直接调用）
- ✅ 与Android保持一致
- ✅ 代码更简洁

## 总结

通过实现双重备用机制，我们确保了MELP在iOS平台的可靠性：
1. 优先使用MethodChannel（更稳定）
2. 回退到FFI（性能更好）
3. 提供详细的错误信息和调试支持

这种方案既解决了当前的符号未找到问题，又为未来的维护和扩展提供了灵活性。
